package config

import (
	"fmt"
	"os"

	"github.com/joho/godotenv"
	"github.com/spf13/viper"
)

// Config stores all configuration of the application.
// The values are read by viper from a config file or environment variable.
type Config struct {
	RPC_URL          string `mapstructure:"RPC_URL"`
	PRIVATE_KEY      string `mapstructure:"PRIVATE_KEY"`
	CONTRACT_ADDRESS string `mapstructure:"CONTRACT_ADDRESS"`
	PORT             string `mapstructure:"PORT"`
	GEMINI_API_KEY   string `mapstructure:"GEMINI_API_KEY"`
	BICONOMY_API_KEY string `mapstructure:"BICONOMY_API_KEY"`
}

// LoadConfig reads configuration from file or environment variables.
func LoadConfig(path string) (config Config, err error) {
	// Load .env file if it exists
	if err := godotenv.Load(path + "/.env"); err != nil {
		fmt.Println("No .env file found, using environment variables")
	}

	viper.AddConfigPath(path)
	viper.SetConfigName("app")
	viper.SetConfigType("env")

	viper.AutomaticEnv()

	// Set default values
	viper.SetDefault("PORT", "8080")
	viper.SetDefault("RPC_URL", "https://sepolia.infura.io/v3/YOUR_KEY")

	err = viper.ReadInConfig()
	if err != nil {
		// If config file not found, continue with env vars only
		fmt.Println("Config file not found, using environment variables only")
	}

	err = viper.Unmarshal(&config)
	if err != nil {
		return
	}

	// Validate required fields
	if config.RPC_URL == "" {
		return config, fmt.Errorf("RPC_URL is required")
	}
	if config.PRIVATE_KEY == "" {
		return config, fmt.Errorf("PRIVATE_KEY is required")
	}
	if config.CONTRACT_ADDRESS == "" {
		return config, fmt.Errorf("CONTRACT_ADDRESS is required")
	}

	return
}
